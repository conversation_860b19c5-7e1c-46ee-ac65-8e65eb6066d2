#!/usr/bin/env node

/**
 * 直接测试 NocoBase 路由 API 的脚本
 * 不通过 MCP，直接调用 NocoBase API
 */

import axios from 'axios';

const config = {
  baseUrl: 'http://nocobase.nocobasedocker.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
  app: 'mcp_playground'
};

async function testNocoBaseConnection() {
  console.log('🚀 开始直接测试 NocoBase 路由 API...\n');

  // 创建 axios 实例
  const client = axios.create({
    baseURL: config.baseUrl,
    headers: {
      'Authorization': `Bearer ${config.token}`,
      'X-App': config.app,
      'Content-Type': 'application/json'
    },
    timeout: 30000 // 30秒超时
  });

  try {
    // 第一步：测试基本连接
    console.log('📡 第一步：测试基本连接...');
    try {
      const healthResponse = await client.get('/app:getInfo');
      console.log('✅ NocoBase 连接成功');
      console.log('📊 应用信息:', JSON.stringify(healthResponse.data, null, 2));
    } catch (healthError) {
      console.log('⚠️  应用信息获取失败，尝试其他端点...');
      console.log('错误:', healthError.message);
    }

    // 第二步：测试路由列表 API
    console.log('\n📋 第二步：测试路由列表 API...');
    try {
      const routesResponse = await client.get('/desktopRoutes:listAccessible?tree=true');
      console.log('✅ 路由列表获取成功');
      
      const routes = routesResponse.data?.data || [];
      console.log(`📊 找到 ${routes.length} 个路由`);
      
      if (routes.length > 0) {
        console.log('\n📋 前5个路由:');
        routes.slice(0, 5).forEach((route, index) => {
          console.log(`  ${index + 1}. ID: ${route.id}, 标题: "${route.title}", 类型: ${route.type}`);
        });
        
        // 第三步：测试获取特定路由
        console.log('\n📋 第三步：测试获取特定路由...');
        const testRouteId = routes[0].id;
        console.log(`🎯 测试路由ID: ${testRouteId}`);
        
        try {
          const routeResponse = await client.get(`/desktopRoutes:get?filterByTk=${testRouteId}`);
          console.log('✅ 特定路由获取成功');
          console.log('📊 路由详情:', JSON.stringify(routeResponse.data.data, null, 2));
          
          // 第四步：测试多个路由ID
          if (routes.length > 1) {
            console.log('\n📋 第四步：测试多个路由ID...');
            const additionalIds = routes.slice(1, Math.min(4, routes.length));
            
            for (const route of additionalIds) {
              try {
                console.log(`🎯 测试路由ID: ${route.id} (${route.title})`);
                const result = await client.get(`/desktopRoutes:get?filterByTk=${route.id}`);
                console.log(`✅ 路由 ${route.id} 获取成功`);
              } catch (error) {
                console.error(`❌ 路由 ${route.id} 获取失败:`, error.message);
              }
            }
          }
          
        } catch (getError) {
          console.error(`❌ 获取路由 ${testRouteId} 失败:`, getError.message);
          console.error('响应状态:', getError.response?.status);
          console.error('响应数据:', getError.response?.data);
        }
        
      } else {
        console.log('⚠️  没有找到任何路由');
      }
      
    } catch (listError) {
      console.error('❌ 路由列表获取失败:', listError.message);
      console.error('响应状态:', listError.response?.status);
      console.error('响应数据:', listError.response?.data);
    }

    // 第五步：测试其他相关 API
    console.log('\n📋 第五步：测试其他相关 API...');
    
    // 测试集合列表
    try {
      console.log('🔧 测试集合列表...');
      const collectionsResponse = await client.get('/collections:list');
      const collections = collectionsResponse.data?.data || [];
      console.log(`✅ 找到 ${collections.length} 个集合`);
      if (collections.length > 0) {
        console.log(`   前3个集合: ${collections.slice(0, 3).map(c => c.name).join(', ')}`);
      }
    } catch (error) {
      console.log('⚠️  集合列表获取失败:', error.message);
    }

    // 测试用户信息
    try {
      console.log('🔧 测试用户信息...');
      const userResponse = await client.get('/auth:check');
      console.log('✅ 用户认证检查成功');
      console.log('👤 当前用户:', userResponse.data?.data?.user?.email || 'Unknown');
    } catch (error) {
      console.log('⚠️  用户信息获取失败:', error.message);
    }

    console.log('\n🎉 NocoBase API 测试完成!');
    console.log('\n📊 测试总结:');
    console.log('- ✅ 基本连接: 正常');
    console.log('- ✅ 路由 API: 正常');
    console.log('- ✅ 认证: 正常');

  } catch (error) {
    console.error('💥 测试过程中发生严重错误:', error);
    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 连接被拒绝 - 请检查 NocoBase 服务器是否运行');
    } else if (error.code === 'ENOTFOUND') {
      console.error('🌐 域名解析失败 - 请检查网络连接和域名配置');
    } else if (error.response?.status === 401) {
      console.error('🔐 认证失败 - 请检查 token 是否有效');
    } else if (error.response?.status === 403) {
      console.error('🚫 权限不足 - 请检查用户权限');
    }
  }
}

// 运行测试
testNocoBaseConnection().catch(error => {
  console.error('💥 测试脚本执行失败:', error);
  process.exit(1);
});
