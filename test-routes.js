#!/usr/bin/env node

/**
 * 测试 NocoBase 路由工具的脚本
 * 验证 list_routes 和 get_route 工具是否正常工作
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testRouteTools() {
  console.log('🚀 开始测试 NocoBase 路由工具...\n');

  // 创建 MCP 客户端，使用测试环境配置
  const transport = new StdioClientTransport({
    command: 'node',
    args: [
      'dist/index.js',
      '--base-url', 'http://nocobase.nocobasedocker.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
      '--app', 'mcp_playground'
    ]
  });

  const client = new Client(
    {
      name: 'route-test-client',
      version: '1.0.0'
    },
    {
      capabilities: {}
    }
  );

  try {
    // 连接到 MCP 服务器
    console.log('📡 连接到 MCP 服务器...');
    await client.connect(transport);
    console.log('✅ 连接成功\n');

    // 获取可用工具列表
    console.log('🔧 获取可用工具列表...');
    const tools = await client.listTools();
    console.log(`✅ 找到 ${tools.tools.length} 个工具:`);
    tools.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // 检查路由相关工具是否存在
    const routeTools = tools.tools.filter(tool => 
      tool.name === 'list_routes' || tool.name === 'get_route'
    );
    
    if (routeTools.length < 2) {
      console.error('❌ 缺少必要的路由工具 (list_routes, get_route)');
      return;
    }
    console.log('✅ 路由工具检查通过\n');

    // 第一步：测试 list_routes 工具
    console.log('📋 第一步：测试 list_routes 工具...');
    try {
      const listResult = await client.callTool('list_routes', {
        tree: true
      });
      
      console.log('✅ list_routes 调用成功');
      console.log('📊 返回结果类型:', typeof listResult.content);
      
      if (listResult.content && Array.isArray(listResult.content)) {
        console.log(`📊 返回内容数量: ${listResult.content.length}`);
        
        // 显示前几个内容项的类型
        listResult.content.slice(0, 3).forEach((item, index) => {
          console.log(`  内容项 ${index + 1}: ${item.type}`);
          if (item.type === 'text' && item.text) {
            // 尝试解析文本中的路由数据
            const lines = item.text.split('\n');
            console.log(`    文本行数: ${lines.length}`);
            console.log(`    前几行: ${lines.slice(0, 3).join(' | ')}`);
          }
        });
      }
      
      console.log('\n📋 完整的 list_routes 响应:');
      console.log(JSON.stringify(listResult, null, 2));
      console.log();

      // 尝试从响应中提取路由ID
      let routeIds = [];
      if (listResult.content && Array.isArray(listResult.content)) {
        for (const item of listResult.content) {
          if (item.type === 'text' && item.text) {
            // 尝试解析JSON格式的路由数据
            try {
              const jsonMatch = item.text.match(/\[[\s\S]*\]/);
              if (jsonMatch) {
                const routes = JSON.parse(jsonMatch[0]);
                if (Array.isArray(routes)) {
                  routeIds = routes.map(route => route.id).filter(id => id != null);
                  console.log(`✅ 从响应中提取到 ${routeIds.length} 个路由ID: ${routeIds.slice(0, 5).join(', ')}${routeIds.length > 5 ? '...' : ''}`);
                  break;
                }
              }
            } catch (parseError) {
              console.log('⚠️  无法解析路由JSON数据，尝试其他方法...');
            }
          }
        }
      }

      // 第二步：测试 get_route 工具
      if (routeIds.length > 0) {
        console.log('\n📋 第二步：测试 get_route 工具...');
        
        // 选择第一个路由ID进行测试
        const testRouteId = routeIds[0];
        console.log(`🎯 测试路由ID: ${testRouteId}`);
        
        try {
          const getResult = await client.callTool('get_route', {
            id: String(testRouteId)
          });
          
          console.log('✅ get_route 调用成功');
          console.log('\n📋 完整的 get_route 响应:');
          console.log(JSON.stringify(getResult, null, 2));
          
          // 验证响应内容
          if (getResult.content && Array.isArray(getResult.content)) {
            console.log(`\n✅ 响应验证通过:`);
            console.log(`  - 内容项数量: ${getResult.content.length}`);
            console.log(`  - 包含路由详情: ${getResult.content.some(item => 
              item.type === 'text' && item.text && item.text.includes('Route details')
            )}`);
          }
          
        } catch (getError) {
          console.error(`❌ get_route 调用失败:`, getError.message);
          console.error('错误详情:', getError);
        }
        
        // 测试多个路由ID（如果有的话）
        if (routeIds.length > 1) {
          console.log('\n📋 第三步：测试多个路由ID...');
          const additionalIds = routeIds.slice(1, Math.min(4, routeIds.length)); // 最多测试3个额外的ID
          
          for (const routeId of additionalIds) {
            try {
              console.log(`🎯 测试路由ID: ${routeId}`);
              const result = await client.callTool('get_route', {
                id: String(routeId)
              });
              console.log(`✅ 路由 ${routeId} 获取成功`);
            } catch (error) {
              console.error(`❌ 路由 ${routeId} 获取失败:`, error.message);
            }
          }
        }
        
      } else {
        console.log('⚠️  未能从 list_routes 响应中提取到路由ID，跳过 get_route 测试');
      }

    } catch (listError) {
      console.error('❌ list_routes 调用失败:', listError.message);
      console.error('错误详情:', listError);
    }

    console.log('\n🎉 路由工具测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 关闭连接
    try {
      await client.close();
      console.log('📡 MCP 连接已关闭');
    } catch (closeError) {
      console.error('⚠️  关闭连接时出错:', closeError);
    }
  }
}

// 运行测试
testRouteTools().catch(error => {
  console.error('💥 测试脚本执行失败:', error);
  process.exit(1);
});
