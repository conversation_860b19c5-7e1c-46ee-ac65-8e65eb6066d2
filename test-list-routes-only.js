#!/usr/bin/env node

/**
 * 只测试 list_routes 工具的脚本
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testListRoutesOnly() {
  console.log('🚀 开始测试 NocoBase list_routes 工具...\n');

  // 创建 MCP 客户端，使用测试环境配置
  const transport = new StdioClientTransport({
    command: 'node',
    args: [
      'dist/index.js',
      '--base-url', 'http://nocobase.nocobasedocker.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTUwNTI2MDEsImV4cCI6MzMzMTI2NTI2MDF9.2IzOVJB0G2VqE2zQpsULLXUhEw87dmLYAtKGcwB_wyw',
      '--app', 'mcp_playground'
    ]
  });

  const client = new Client(
    {
      name: 'list-routes-test-client',
      version: '1.0.0'
    },
    {
      capabilities: {}
    }
  );

  try {
    // 连接到 MCP 服务器
    console.log('📡 连接到 MCP 服务器...');
    await client.connect(transport);
    console.log('✅ 连接成功\n');

    // 测试 list_routes 工具
    console.log('📋 测试 list_routes 工具...');
    try {
      const listResult = await client.callTool('list_routes', {
        tree: true
      });
      
      console.log('✅ list_routes 调用成功');
      console.log('📊 返回结果:');
      console.log(JSON.stringify(listResult, null, 2));
      
      // 尝试解析路由数据
      if (listResult.content && Array.isArray(listResult.content)) {
        for (const item of listResult.content) {
          if (item.type === 'text' && item.text) {
            // 查找包含路由数据的部分
            const lines = item.text.split('\n');
            console.log('\n📋 解析的文本内容:');
            lines.forEach((line, index) => {
              if (index < 10) { // 只显示前10行
                console.log(`  ${index + 1}: ${line}`);
              }
            });
            
            // 尝试提取JSON数据
            try {
              const jsonMatch = item.text.match(/\[[\s\S]*\]/);
              if (jsonMatch) {
                const routes = JSON.parse(jsonMatch[0]);
                if (Array.isArray(routes)) {
                  console.log(`\n✅ 成功解析到 ${routes.length} 个路由:`);
                  routes.forEach((route, index) => {
                    console.log(`  ${index + 1}. ID: ${route.id}, 标题: "${route.title}", 类型: ${route.type}`);
                  });
                  
                  // 现在测试 get_route，但使用更短的超时时间
                  if (routes.length > 0) {
                    console.log('\n📋 测试 get_route 工具（短超时）...');
                    const testRouteId = routes[0].id;
                    console.log(`🎯 测试路由ID: ${testRouteId}`);
                    
                    try {
                      // 设置较短的超时时间
                      const getResult = await Promise.race([
                        client.callTool('get_route', { id: String(testRouteId) }),
                        new Promise((_, reject) => 
                          setTimeout(() => reject(new Error('自定义超时')), 10000) // 10秒超时
                        )
                      ]);
                      
                      console.log('✅ get_route 调用成功');
                      console.log('📊 路由详情:');
                      console.log(JSON.stringify(getResult, null, 2));
                      
                    } catch (getError) {
                      if (getError.message === '自定义超时') {
                        console.log('⚠️  get_route 调用超时（10秒）');
                      } else {
                        console.log(`❌ get_route 调用失败: ${getError.message}`);
                        if (getError.message.includes('No permissions')) {
                          console.log('🔐 这是权限问题，说明API端点正确但用户权限不足');
                        }
                      }
                    }
                  }
                }
              }
            } catch (parseError) {
              console.log('⚠️  无法解析JSON数据，但这不影响工具功能');
            }
            break; // 只处理第一个文本项
          }
        }
      }

    } catch (listError) {
      console.error('❌ list_routes 调用失败:', listError.message);
      console.error('错误详情:', listError);
    }

    console.log('\n🎉 测试完成!');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    // 关闭连接
    try {
      await client.close();
      console.log('📡 MCP 连接已关闭');
    } catch (closeError) {
      console.error('⚠️  关闭连接时出错:', closeError);
    }
  }
}

// 运行测试
testListRoutesOnly().catch(error => {
  console.error('💥 测试脚本执行失败:', error);
  process.exit(1);
});
